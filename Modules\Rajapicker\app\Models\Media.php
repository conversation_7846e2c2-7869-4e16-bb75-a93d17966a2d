<?php
namespace Modules\Rajapicker\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\MediaCollections\Models\Media as ModelsMedia;


class Media extends ModelsMedia
{

    protected $fillable = [
        'id',
        'user_id',
        'model_type',
        'model_id',
        'uuid',
        'collection_name',
        'name',
        'file_name',
        'mime_type',
        'disk',
        'conversions_disk',
        'size',
        'manipulations',
        'custom_properties',
        'generated_conversions',
        'responsive_images',
        'order_column',
        'created_at',
        'updated_at',

    ];


     protected static function booted()
    {
       

        static::creating(function ($model) {
            $model->user_id = \Illuminate\Support\Facades\Auth::id();
        });

        static::updating(function ($model) {
            $model->user_id = \Illuminate\Support\Facades\Auth::id();
        });
    }
    /**
     * Default values for attributes
     */
    protected $attributes = [
        'disk' => 'local',
        'conversions_disk' => 'local',
        'model_type' => 'standalone',
        'model_id' => null,
    ];

    /**
     * Mendapatkan URL lengkap untuk file media (dengan /storage/ prefix untuk display)
     */
    public function getUrlAttribute(): ?string
    {
        if (empty($this->file_name)) {
            return null;
        }

        // Jika ini adalah gambar, coba gunakan thumbnail
        if ($this->isImage()) {
            $thumbnailUrl = $this->getThumbnailUrl();
            if ($thumbnailUrl) {
                return $thumbnailUrl;
            }
        }

        // Gunakan disk yang ditentukan di record
        $disk = $this->disk ?? 'public';

        // Buat path berdasarkan CustomPathGenerator (collection_name/file_name)
        $path = $this->collection_name . '/' . $this->file_name;

        // Jika menggunakan disk public, gunakan prefix dari config
        if ($disk === 'public') {
            $prefix = config('media-library.prefix', 'uploads');
            return asset('storage/' . $prefix . '/' . $path);
        }

        // Untuk disk lain, gunakan asset dengan path yang sama
        return asset('storage/' . $path);
    }

    /**
     * Mendapatkan URL relatif tanpa /storage/ prefix dan leading slash (untuk penyimpanan database)
     */
    public function getRelativeUrlAttribute(): ?string
    {
        if (empty($this->file_name)) {
            return null;
        }

        // Buat path berdasarkan CustomPathGenerator (collection_name/file_name)
        $path = $this->collection_name . '/' . $this->file_name;

        // Gunakan prefix dari config, tanpa leading slash
        $prefix = config('media-library.prefix', 'uploads');
        return $prefix . '/' . $path;
    }

    /**
     * Override getUrl method untuk menangani error
     */
    public function getUrl(string $conversionName = ''): string
    {
        try {
            // Coba gunakan method parent
            return parent::getUrl($conversionName);
        } catch (\Exception $e) {
            // Fallback ke accessor custom
            return $this->getUrlAttribute() ?? '';
        }
    }

  /**
   * Cek apakah file adalah gambar
   */
  public function isImage(): bool
  {
      return str_starts_with($this->mime_type ?? '', 'image/');
  }

  /**
   * Mendapatkan path lengkap file
   */
  public function getFullPath(): string
  {
      return $this->id . '/' . $this->file_name;
  }

  /**
   * Mendapatkan URL thumbnail jika ada
   */
  public function getThumbnailUrl(): ?string
  {
      if (!$this->isImage() || empty($this->file_name)) {
          return null;
      }

      // Buat path thumbnail berdasarkan pattern yang ada
      $pathInfo = pathinfo($this->file_name);
      $filename = $pathInfo['filename'];
      $extension = $pathInfo['extension'];

      // Coba thumbnail dengan pattern: collection_name/thumbnails/filename_th.extension
      $thumbnailRelativePath = $this->collection_name . '/thumbnails/' . $filename . '_th.' . $extension;
      $thumbnailPath = public_path('storage/uploads/' . $thumbnailRelativePath);

      if (file_exists($thumbnailPath)) {
          return asset('storage/uploads/' . $thumbnailRelativePath);
      }

      return null;
  }
}