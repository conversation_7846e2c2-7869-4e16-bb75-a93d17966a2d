<?php

use App\Models\Produk;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Config;
// MOVED TO MODULE - BACKUP: use App\Http\Controllers\Api\MediaController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// TIDAK DIGUNAKAN: Endpoint default Laravel Sanctum yang tidak digunakan dalam aplikasi
// Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
//     return $request->user();
// });

// DIGUNAKAN: Endpoint ini digunakan oleh komponen RajaEditor untuk mendapatkan konten modul
Route::get('/get-modul', function (Request $request) {
    $modulName = $request->query('modul');

    if (!$modulName) {
        return response()->json([
            'success' => false,
            'message' => 'Nama modul tidak diberikan'
        ]);
    }

    $temaAktif = Config::get('tema.aktif', 'default');
    $modulPath = public_path('tema/' . $temaAktif . '/modul/' . $modulName . '.blade.php');

    if (!File::exists($modulPath)) {
        return response()->json([
            'success' => false,
            'message' => 'File modul tidak ditemukan'
        ]);
    }

    $content = File::get($modulPath);

    return response()->json([
        'success' => true,
        'content' => $content
    ]);
});



// DIGUNAKAN: Endpoint ini digunakan oleh komponen RajaEditor untuk mendapatkan daftar modul
Route::get('/get-modul-list', function () {
    $temaAktif = Config::get('tema.aktif', 'default');
    $modulDir = public_path('tema/' . $temaAktif . '/modul');

    // Buat direktori jika belum ada
    if (!File::exists($modulDir)) {
        File::makeDirectory($modulDir, 0755, true);
    }

    // Dapatkan semua file .blade.php di direktori modul
    $files = File::files($modulDir);
    $modules = [];

    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'php') {
            $modules[] = [
                'name' => str_replace('.blade.php', '', $file->getFilename()),
                'path' => $file->getPathname(),
                'size' => $file->getSize(),
                'modified' => date('Y-m-d H:i:s', $file->getMTime())
            ];
        }
    }

    return response()->json([
        'success' => true,
        'modules' => $modules
    ]);
});

// DIGUNAKAN: Endpoint ini digunakan oleh komponen RajaEditor untuk membuat modul baru
Route::post('/create-modul', function (Request $request) {
    $modulName = $request->input('modul');
    $content = $request->input('content', '<div class="container">\n    <h1>Modul Baru</h1>\n    <p>Ini adalah modul baru yang dibuat melalui RajaEditor.</p>\n</div>');

    if (!$modulName) {
        return response()->json([
            'success' => false,
            'message' => 'Nama modul tidak diberikan'
        ]);
    }

    // Normalisasi nama modul
    $modulName = strtolower(preg_replace('/[^a-zA-Z0-9_-]/', '', str_replace(' ', '_', $modulName)));

    $temaAktif = Config::get('tema.aktif', 'default');
    $modulDir = public_path('tema/' . $temaAktif . '/modul');

    // Buat direktori jika belum ada
    if (!File::exists($modulDir)) {
        File::makeDirectory($modulDir, 0755, true);
    }

    $modulPath = $modulDir . '/' . $modulName . '.blade.php';

    // Periksa apakah file sudah ada
    if (File::exists($modulPath)) {
        return response()->json([
            'success' => false,
            'message' => 'Modul dengan nama tersebut sudah ada'
        ]);
    }

    try {
        File::put($modulPath, $content);

        return response()->json([
            'success' => true,
            'message' => 'Modul berhasil dibuat',
            'modulName' => $modulName,
            'modulPath' => $modulPath
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Gagal membuat modul: ' . $e->getMessage()
        ]);
    }
});

// DIGUNAKAN: Endpoint ini digunakan oleh komponen RajaEditor untuk menyimpan modul
Route::post('/save-modul', function (Request $request) {
    $modulName = $request->input('modul');
    $html = $request->input('html', '');
    $css = $request->input('css', '');
    $js = $request->input('js', '');
    $saveOption = $request->input('saveOption', 'htmlcss');

    if (!$modulName) {
        return response()->json([
            'success' => false,
            'message' => 'Nama modul tidak diberikan'
        ]);
    }

    // Normalisasi nama modul
    $modulName = strtolower(preg_replace('/[^a-zA-Z0-9_-]/', '', str_replace(' ', '_', $modulName)));

    $temaAktif = Config::get('tema.aktif', 'default');
    $modulDir = public_path('tema/' . $temaAktif . '/modul');

    // Buat direktori jika belum ada
    if (!File::exists($modulDir)) {
        File::makeDirectory($modulDir, 0755, true);
    }

    $modulPath = $modulDir . '/' . $modulName . '.blade.php';

    try {
        // Buat konten yang akan disimpan berdasarkan pilihan penyimpanan
        $contentToSave = '';

        switch ($saveOption) {
            case 'html':
                $contentToSave = $html;
                break;

            case 'css':
                $contentToSave = $css;
                break;

            case 'js':
                $contentToSave = $js;
                break;

            case 'htmlcss':
                $contentToSave = $html;
                if (!empty($css)) {
                    $contentToSave .= "\n\n<style>\n" . $css . "\n</style>";
                }
                break;

            case 'semua':
            default:
                $contentToSave = $html;
                if (!empty($css)) {
                    $contentToSave .= "\n\n<style>\n" . $css . "\n</style>";
                }
                if (!empty($js)) {
                    $contentToSave .= "\n\n<script>\n" . $js . "\n</script>";
                }
                break;
        }

        // Hapus tag <body> dan </body>
        $contentToSave = preg_replace('/<body\b[^>]*>/', '', $contentToSave);
        $contentToSave = preg_replace('/<\/body>/', '', $contentToSave);

        // Simpan konten ke file
        File::put($modulPath, $contentToSave);

        return response()->json([
            'success' => true,
            'message' => 'Modul berhasil disimpan',
            'modulName' => $modulName,
            'modulPath' => $modulPath
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Gagal menyimpan modul: ' . $e->getMessage()
        ]);
    }
});

// DIGUNAKAN: Endpoint ini digunakan oleh komponen Livewire DetailProduk dan DetailProdukModal
Route::get('/produk/{id}/detail', function ($id) {
    $produk = Produk::findOrFail($id);
    return response()->json([
        'id' => $produk->id,
        'nama' => $produk->nama,
        'harga' => $produk->harga,
        'gambar' => $produk->gambar ? asset('storage/' . $produk->gambar) : null,
        'ket' => $produk->ket,
        'fasilitas' => $produk->fasilitas,
        'spesifikasi' => $produk->spesifikasi,
    ]);
});

// MOVED TO MODULE - BACKUP: API Routes untuk RajaPicker - Media Management
/*
Route::prefix('media')->group(function () {
    // Get single media by ID
    Route::get('/{id}', [MediaController::class, 'show']);

    // Get multiple media by IDs
    Route::get('/by-ids', [MediaController::class, 'getByIds']);

    // Get media by collection
    Route::get('/collection/{collection}', [MediaController::class, 'getByCollection']);

    // Upload media files
Route::post('/upload', [MediaController::class, 'upload']);

// Temporary upload
Route::post('/temporary-upload', [\App\Http\Controllers\TemporaryUploadController::class, 'upload']);
});
*/

