<?php

use Illuminate\Support\Facades\Route;
use <PERSON><PERSON><PERSON>\Rajapicker\Http\Controllers\RajapickerController;
use <PERSON><PERSON><PERSON>\Rajapicker\Http\Controllers\Api\MediaController;
use <PERSON><PERSON><PERSON>\Rajapicker\Http\Controllers\Api\ImageEditorController;
use <PERSON><PERSON><PERSON>\Rajapicker\Http\Controllers\RajaPickerThumbnailController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::middleware(['auth:sanctum'])->prefix('v1')->group(function () {
    Route::apiResource('rajapicker', RajapickerController::class)->names('rajapicker');
});

// MOVED FROM MAIN API ROUTES - Media Management for RajaPicker
// Add web middleware for session-based authentication (for Filament)
Route::middleware(['web', 'auth'])->prefix('media')->group(function () {
    // Get all available collections - MUST BE FIRST
    Route::get('/collections', [MediaController::class, 'getCollections']);

    // Get all images from all collections (excluding conversion) with pagination - MUST BE SECOND
    Route::get('/all-images', [MediaController::class, 'getAllImages']);

    // Get multiple media by IDs
    Route::get('/by-ids', [MediaController::class, 'getByIds']);

    // Get media by collection
    Route::get('/collection/{collection}', [MediaController::class, 'getByCollection']);

    // Upload media files
    Route::post('/upload', [MediaController::class, 'upload']);

    // Upload files to temporary storage
    Route::post('/upload-temporary', [MediaController::class, 'uploadTemporary']);

    // Serve temporary file for preview
    Route::get('/temporary-preview', [MediaController::class, 'temporaryPreview']);

    // Get single media by ID - MUST BE LAST (catches all other patterns)
    Route::get('/{id}', [MediaController::class, 'show']);
});

// Image Editor API Routes
Route::middleware(['web', 'auth'])->prefix('image-editor')->group(function () {
    // Get media for editing
    Route::get('/media/{id}', [ImageEditorController::class, 'getMedia']);

    // Get available media for selection
    Route::get('/media', [ImageEditorController::class, 'getAvailableMedia']);

    // Save edited image
    Route::post('/save', [ImageEditorController::class, 'saveEditedImage']);
});

/*
|--------------------------------------------------------------------------
| RajaPicker API Routes
|--------------------------------------------------------------------------
|
| Routes untuk API RajaPicker
|
*/

Route::prefix('rajapicker')->group(function () {
    
    // Thumbnail API Routes
    Route::prefix('thumbnails')->group(function () {
        
        // Generate thumbnail
        Route::post('/generate', [RajaPickerThumbnailController::class, 'generate'])
            ->name('rajapicker.thumbnails.generate');
        
        // Generate semua thumbnail
        Route::post('/generate-all', [RajaPickerThumbnailController::class, 'generateAll'])
            ->name('rajapicker.thumbnails.generate-all');
        
        // Dapatkan URL thumbnail
        Route::get('/url', [RajaPickerThumbnailController::class, 'getUrl'])
            ->name('rajapicker.thumbnails.url');
        
        // Dapatkan semua URL thumbnail
        Route::get('/urls', [RajaPickerThumbnailController::class, 'getAllUrls'])
            ->name('rajapicker.thumbnails.urls');
        
        // Regenerate thumbnail
        Route::put('/regenerate', [RajaPickerThumbnailController::class, 'regenerate'])
            ->name('rajapicker.thumbnails.regenerate');
        
        // Regenerate semua thumbnail
        Route::put('/regenerate-all', [RajaPickerThumbnailController::class, 'regenerateAll'])
            ->name('rajapicker.thumbnails.regenerate-all');
        
        // Hapus thumbnail
        Route::delete('/delete', [RajaPickerThumbnailController::class, 'delete'])
            ->name('rajapicker.thumbnails.delete');
        
        // Batch generate thumbnail
        Route::post('/batch-generate', [RajaPickerThumbnailController::class, 'batchGenerate'])
            ->name('rajapicker.thumbnails.batch-generate');
        
        // Cleanup thumbnail yang tidak terpakai
        Route::delete('/cleanup', [RajaPickerThumbnailController::class, 'cleanup'])
            ->name('rajapicker.thumbnails.cleanup');
        
        // Dapatkan konfigurasi thumbnail
        Route::get('/config', [RajaPickerThumbnailController::class, 'config'])
            ->name('rajapicker.thumbnails.config');
    });
});
