<?php

namespace Modules\Rajapicker\Filament\Forms\Components;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class RajaPickerExt extends RajaPicker
{
    protected string $view = 'rajapicker::components.forms.raja-picker-ext';
    
    // Flag untuk menandai bahwa ini menggunakan temporary upload
    protected bool $useTemporaryUpload = true;
    
    // Array untuk menyimpan file temporary yang akan dipindahkan saat submit
    protected array $temporaryFiles = [];

    // Array untuk tracking file yang sudah dipindahkan (mencegah duplikasi)
    protected array $processedFiles = [];

    protected function setUp(): void
    {
        parent::setUp();

        // Handle temporary files saat state berubah
        $this->afterStateUpdated(function ($state, $set) {
            Log::info('RajaPickerExt: afterStateUpdated called', [
                'state' => $state,
                'statePath' => $this->getStatePath()
            ]);

            // Cek apakah ada temporary files yang perlu dipindahkan
            $this->extractTemporaryFilesFromState($state);

            Log::info('RajaPickerExt: After extractTemporaryFilesFromState', [
                'temporaryFiles' => $this->temporaryFiles
            ]);

            // Jika ada temporary files, pindahkan ke lokasi final
            if (!empty($this->temporaryFiles)) {
                $newState = $this->moveTemporaryFilesToFinal($state);
                Log::info('RajaPickerExt: After moveTemporaryFilesToFinal', [
                    'newState' => $newState
                ]);

                // Update state dengan URL final
                if ($newState !== $state) {
                    $set($this->getStatePath(), $newState);
                }
            }
        });

        // Override dehydration untuk handle temporary files
        $this->dehydrateStateUsing(function ($state) {
            Log::info('RajaPickerExt: Starting dehydrateStateUsing', [
                'state' => $state,
                'statePath' => $this->getStatePath()
            ]);

            // Cek apakah ada temporary files yang perlu dipindahkan
            $this->extractTemporaryFilesFromState($state);

            Log::info('RajaPickerExt: After extractTemporaryFilesFromState', [
                'temporaryFiles' => $this->temporaryFiles
            ]);

            // Jika ada temporary files, pindahkan ke lokasi final
            if (!empty($this->temporaryFiles)) {
                $state = $this->moveTemporaryFilesToFinal($state);
                Log::info('RajaPickerExt: After moveTemporaryFilesToFinal', [
                    'newState' => $state
                ]);
            }

            // Jika state adalah string JSON, decode dulu
            if (is_string($state) && $this->isJson($state)) {
                $state = json_decode($state, true);
            }

            // Konversi ID ke URL jika diperlukan
            $state = $this->convertIdsToUrls($state);

            // Clean URLs for storage (remove /storage/ prefix)
            $state = $this->cleanUrlsForStorage($state);

            // Untuk multiple selection, pastikan return array
            if ($this->multiple) {
                return is_array($state) ? $state : ($state ? [$state] : []);
            }

            // Untuk single selection, pastikan return single value
            return is_array($state) ? (count($state) > 0 ? $state[0] : null) : $state;
        });
    }

    /**
     * Extract temporary files information from state
     */
    protected function extractTemporaryFilesFromState($state)
    {
        // Jika temporary files sudah ada, skip
        if (!empty($this->temporaryFiles)) {
            return;
        }

        $stateArray = is_array($state) ? $state : [$state];

        foreach ($stateArray as $url) {
            if (is_string($url) && strpos($url, 'livewire-tmp') !== false) {
                // Extract filename dari URL
                $filename = basename($url);
                $tempPath = 'livewire-tmp/rajapicker/' . $filename;

                // Coba dapatkan original name dari filename
                $parts = explode('_', $filename);
                if (count($parts) >= 3) {
                    // Format: originalname_timestamp_uniqid.ext
                    $originalName = $parts[0] . '.' . pathinfo($filename, PATHINFO_EXTENSION);
                } else {
                    $originalName = $filename;
                }

                $this->temporaryFiles[] = [
                    'temp_path' => $tempPath,
                    'original_name' => $originalName,
                    'temporary_url' => $url,
                    'added_at' => now()
                ];
            }
        }
    }

    /**
     * Pindahkan file temporary ke lokasi final dan buat thumbnail
     */
    protected function moveTemporaryFilesToFinal($state)
    {
        Log::info('RajaPickerExt: moveTemporaryFilesToFinal called', [
            'state' => $state,
            'temporaryFiles' => $this->temporaryFiles,
            'collection' => $this->collection
        ]);

        // Jika tidak ada temporary files atau state kosong, return state asli
        if (empty($this->temporaryFiles) || empty($state)) {
            Log::info('RajaPickerExt: No temporary files or empty state, returning original state');
            return $state;
        }

        $finalUrls = [];
        $stateArray = is_array($state) ? $state : [$state];

        Log::info('RajaPickerExt: Processing state array', ['stateArray' => $stateArray]);

        // Loop melalui state untuk mencari temporary URLs
        foreach ($stateArray as $url) {
            Log::info('RajaPickerExt: Processing URL', ['url' => $url]);

            if (strpos($url, 'livewire-tmp') !== false) {
                Log::info('RajaPickerExt: Found temporary URL, searching for temp file');

                // Ini adalah temporary file, perlu dipindahkan
                $tempFile = $this->findTemporaryFileByUrl($url);
                if ($tempFile) {
                    Log::info('RajaPickerExt: Temp file found, moving to final location', ['tempFile' => $tempFile]);

                    try {
                        $finalUrl = $this->moveTemporaryFileToFinal($tempFile);
                        Log::info('RajaPickerExt: File moved successfully', ['finalUrl' => $finalUrl]);

                        if ($finalUrl) {
                            $finalUrls[] = $finalUrl;
                        }
                    } catch (\Exception $e) {
                        Log::error('Error moving temporary file: ' . $e->getMessage(), [
                            'temp_file' => $tempFile,
                            'url' => $url,
                            'trace' => $e->getTraceAsString()
                        ]);
                        // Jika gagal, tetap gunakan URL asli
                        $finalUrls[] = $url;
                    }
                } else {
                    Log::warning('RajaPickerExt: Temporary file not found for URL', ['url' => $url]);
                    // Temporary file tidak ditemukan, gunakan URL asli
                    $finalUrls[] = $url;
                }
            } else {
                Log::info('RajaPickerExt: Not a temporary URL, using original', ['url' => $url]);
                // Bukan temporary file, gunakan URL asli
                $finalUrls[] = $url;
            }
        }

        // Clear temporary files array
        $this->temporaryFiles = [];

        Log::info('RajaPickerExt: Final URLs prepared', [
            'finalUrls' => $finalUrls,
            'isMultiple' => $this->multiple
        ]);

        // Return final URLs
        if ($this->multiple) {
            return $finalUrls;
        } else {
            return count($finalUrls) > 0 ? $finalUrls[0] : null;
        }
    }

    /**
     * Cari temporary file berdasarkan URL
     */
    protected function findTemporaryFileByUrl(string $url): ?array
    {
        foreach ($this->temporaryFiles as $tempFile) {
            if (isset($tempFile['temporary_url']) && $tempFile['temporary_url'] === $url) {
                return $tempFile;
            }
        }
        return null;
    }

    /**
     * Pindahkan satu temporary file ke lokasi final
     */
    protected function moveTemporaryFileToFinal(array $tempFile): ?string
    {
        $originalName = $tempFile['original_name'];
        $tempPath = $tempFile['temp_path'];

        // Cek apakah file sudah pernah diproses menggunakan session
        $sessionKey = 'rajapicker_processed_files';
        $processedFiles = session($sessionKey, []);

        if (in_array($tempPath, $processedFiles)) {
            Log::info('RajaPickerExt: File already processed, finding existing media', ['tempPath' => $tempPath]);

            // File sudah diproses, cari media record yang sudah ada
            $record = $this->getRecord();
            if ($record) {
                $existingMedia = $record->getMedia($this->collection)->last();
                if ($existingMedia) {
                    Log::info('RajaPickerExt: Found existing media record', [
                        'mediaId' => $existingMedia->id,
                        'fileName' => $existingMedia->file_name
                    ]);
                    return $this->removeStoragePrefix($existingMedia->url);
                }
            }

            Log::warning('RajaPickerExt: File processed but no media found');
            return null;
        }

        // Buat path final berdasarkan collection
        $finalPath = $this->generateFinalPath($originalName);

        // Pindahkan file dari temporary ke final location
        if (Storage::exists($tempPath)) {
            Log::info('RajaPickerExt: File exists, starting copy process', [
                'tempPath' => $tempPath,
                'finalPath' => $finalPath
            ]);

            // Copy file dari default disk ke public disk
            $tempContent = Storage::get($tempPath);
            Log::info('RajaPickerExt: Got temp content', [
                'contentSize' => strlen($tempContent)
            ]);

            // Pastikan direktori ada
            $directory = dirname($finalPath);
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
                Log::info('RajaPickerExt: Created directory', ['directory' => $directory]);
            }

            $putResult = Storage::disk('public')->put($finalPath, $tempContent);
            Log::info('RajaPickerExt: Put file result', [
                'result' => $putResult,
                'finalPath' => $finalPath
            ]);

            // Hapus file temporary
            Storage::delete($tempPath);
            Log::info('RajaPickerExt: Deleted temp file');

            // Tandai file sebagai sudah diproses menggunakan session
            $processedFiles[] = $tempPath;
            session([$sessionKey => $processedFiles]);

            // Buat record media di database
            $mediaRecord = $this->createMediaRecord($finalPath, $originalName);

            // Generate thumbnail jika diaktifkan
            if ($mediaRecord) {
                $this->generateThumbnailsForMedia($mediaRecord);
                return $this->removeStoragePrefix($mediaRecord->url);
            }
        } else {
            Log::warning('RajaPickerExt: Temp file does not exist', [
                'tempPath' => $tempPath
            ]);
        }

        return null;
    }

    /**
     * Generate path final untuk file
     */
    protected function generateFinalPath(string $originalName): string
    {
        $pathInfo = pathinfo($originalName);
        $name = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? 'jpg';
        
        // Generate unique filename
        $uniqueName = $name . '_' . time() . '_' . uniqid() . '.' . $extension;
        
        // Path berdasarkan collection
        return 'uploads/' . $this->collection . '/' . $uniqueName;
    }

    /**
     * Buat record media di database menggunakan Spatie Media Library
     */
    protected function createMediaRecord(string $filePath, string $originalName)
    {
        try {
            // Dapatkan model yang sedang diedit dari form
            $record = $this->getRecord();

            if (!$record) {
                Log::error('RajaPickerExt: No record found for media creation');
                return null;
            }

            // Gunakan Spatie Media Library untuk menambahkan media
            // File sudah ada di storage/app/uploads/cms/ (disk 'local')
            $fullPath = storage_path('app/' . $filePath);

            Log::info('RajaPickerExt: Creating media record', [
                'fullPath' => $fullPath,
                'filePath' => $filePath,
                'originalName' => $originalName,
                'collection' => $this->collection,
                'recordType' => get_class($record),
                'recordId' => $record->id
            ]);

            $mediaRecord = $record
                ->addMedia($fullPath)
                ->usingName(pathinfo($originalName, PATHINFO_FILENAME))
                ->usingFileName(basename($filePath))
                ->toMediaCollection($this->collection, 'local');

            Log::info('RajaPickerExt: Media record created successfully', [
                'mediaId' => $mediaRecord->id,
                'fileName' => $mediaRecord->file_name,
                'recordType' => get_class($record),
                'recordId' => $record->id
            ]);

            // Set custom properties jika diperlukan
            if (Auth::check()) {
                $mediaRecord->setCustomProperty('user_id', Auth::id());
                $mediaRecord->save();
            }

            return $mediaRecord;
        } catch (\Exception $e) {
            Log::error('Error creating media record: ' . $e->getMessage(), [
                'file_path' => $filePath,
                'original_name' => $originalName,
                'collection' => $this->collection,
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Generate thumbnails untuk media
     */
    protected function generateThumbnailsForMedia($mediaRecord)
    {
        try {
            $thumbnailService = $this->getThumbnailService();
            $originalPath = 'uploads/' . $this->collection . '/' . $mediaRecord->file_name;
            
            // Generate semua ukuran thumbnail yang diaktifkan
            $thumbnailService->generateAllThumbnails($originalPath);
            
        } catch (\Exception $e) {
            Log::error('Error generating thumbnails: ' . $e->getMessage(), [
                'media_id' => $mediaRecord->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Tambahkan file temporary ke array
     */
    public function addTemporaryFile(string $tempPath, string $originalName): self
    {
        $this->temporaryFiles[] = [
            'temp_path' => $tempPath,
            'original_name' => $originalName,
            'added_at' => now()
        ];
        
        return $this;
    }

    /**
     * Get temporary files
     */
    public function getTemporaryFiles(): array
    {
        return $this->temporaryFiles;
    }

    /**
     * Clear temporary files
     */
    public function clearTemporaryFiles(): self
    {
        $this->temporaryFiles = [];
        return $this;
    }

    /**
     * Check if using temporary upload
     */
    public function isUsingTemporaryUpload(): bool
    {
        return $this->useTemporaryUpload;
    }

    /**
     * Set temporary upload mode
     */
    public function useTemporaryUpload(bool $use = true): self
    {
        $this->useTemporaryUpload = $use;
        return $this;
    }

    /**
     * Convert IDs to URLs (without /storage/ prefix for storage)
     */
    private function convertIdsToUrls($state)
    {
        if (empty($state)) {
            return $state;
        }

        // Jika sudah berupa URL, clean it for storage
        if (is_string($state) && (str_starts_with($state, 'http') || str_starts_with($state, '/'))) {
            return $this->cleanUrlForStorage($state);
        }

        if (is_array($state)) {
            $urls = [];
            foreach ($state as $item) {
                if (is_numeric($item)) {
                    $media = Media::find($item);
                    if ($media) {
                        // Use relative URL (without /storage/ prefix) for storage
                        $urls[] = $media->relative_url ?? $this->cleanUrlForStorage($media->url);
                    }
                } elseif (is_string($item) && (str_starts_with($item, 'http') || str_starts_with($item, '/'))) {
                    $urls[] = $this->cleanUrlForStorage($item);
                }
            }
            return $urls;
        } elseif (is_numeric($state)) {
            $media = Media::find($state);
            return $media ? ($media->relative_url ?? $this->cleanUrlForStorage($media->url)) : null;
        }

        return $state;
    }

    /**
     * Remove /storage/ prefix from URL for database storage
     */
    private function removeStoragePrefix(string $url): string
    {
        if (str_starts_with($url, '/storage/')) {
            $cleanUrl = substr($url, 8); // Remove '/storage' (8 characters)
            // Remove leading slash for cleaner storage
            if (str_starts_with($cleanUrl, '/')) {
                $cleanUrl = substr($cleanUrl, 1);
            }
            return $cleanUrl;
        }
        return $url;
    }

    /**
     * Clean URL for storage (remove /storage/ prefix and leading slash)
     */
    private function cleanUrlForStorage($url): ?string
    {
        if (empty($url) || !is_string($url)) {
            return $url;
        }

        // Remove /storage/ prefix if present
        $cleanUrl = $this->removeStoragePrefix($url);

        // Remove leading slash for cleaner storage
        if (str_starts_with($cleanUrl, '/') && !str_starts_with($cleanUrl, 'http')) {
            $cleanUrl = substr($cleanUrl, 1);
        }

        return $cleanUrl;
    }

    /**
     * Clean URLs for storage (handle both single URL and array of URLs)
     */
    private function cleanUrlsForStorage($state)
    {
        if (empty($state)) {
            return $state;
        }

        if (is_array($state)) {
            return array_map(function($url) {
                return $this->cleanUrlForStorage($url);
            }, $state);
        }

        return $this->cleanUrlForStorage($state);
    }

    /**
     * Check if string is valid JSON
     */
    protected function isJson(string $string): bool
    {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }
}
