@php
    $statePath = $getStatePath();
    $isMultiple = $isMultiple();
    $collection = $getCollection();
    $maxFileSize = $getMaxFileSize();
    $acceptedTypes = implode(',', $getAcceptedFileTypes());
    $previewSize = $getPreviewSize();
    $state = $getState();
    
    // Pastikan state dalam format yang benar
    if ($isMultiple && !is_array($state)) {
        $state = $state ? [$state] : [];
    } elseif (!$isMultiple && is_array($state)) {
        $state = count($state) > 0 ? $state[0] : null;
    }
@endphp

<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
  <div class="raja-picker-ext-container" x-data="rajaPickerExt({
      statePath: '{{ $statePath }}',
      isMultiple: {{ $isMultiple ? 'true' : 'false' }},
      collection: '{{ $collection }}',
      maxFileSize: {{ $maxFileSize }},
      acceptedTypes: '{{ $acceptedTypes }}',
      enablePicker: {{ $isPickerEnabled() ? 'true' : 'false' }},
      enableUploader: {{ $isUploaderEnabled() ? 'true' : 'false' }},
      previewSize: {{ $previewSize }},
      byUser: {{ $isByUser() ? 'true' : 'false' }},
      byUserId: {{ $getByUserId() ?? 'null' }},
      convertWebp: {{ $shouldConvertWebp() ? 'true' : 'false' }},
      currentValue: @js($state),
      useTemporaryUpload: true
  })">
    <!-- Hidden input untuk menyimpan nilai -->
    <input
        type="hidden"
        name="{{ $statePath }}"
        {{ $applyStateBindingModifiers('wire:model') }}="{{ $getStatePath() }}"
        x-model="value"
    />

    <!-- Container utama -->
    <div class="space-y-3">
        <!-- Preview area -->
        <div x-show="hasValue" class="space-y-2">
            <!-- Single preview -->
            <template x-if="!isMultiple && hasValue">
                <div class="relative inline-block">
                    <img 
                        :src="getPreviewUrl(value)" 
                        :alt="getFileName(value)"
                        class="rounded-lg border border-gray-200 object-cover"
                        :style="`width: ${previewSize}px; height: ${previewSize}px;`"
                    />
                    <button 
                        type="button"
                        @click="removeValue()"
                        class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                    >
                        ×
                    </button>
                </div>
            </template>

            <!-- Multiple preview -->
            <template x-if="isMultiple && hasValue">
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                    <template x-for="(item, index) in value" :key="index">
                        <div class="relative">
                            <img 
                                :src="getPreviewUrl(item)" 
                                :alt="getFileName(item)"
                                class="w-full rounded-lg border border-gray-200 object-cover"
                                :style="`height: ${previewSize}px;`"
                            />
                            <button 
                                type="button"
                                @click="removeValueAt(index)"
                                class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                            >
                                ×
                            </button>
                        </div>
                    </template>
                </div>
            </template>
        </div>

        <!-- Upload area -->
        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
            <div class="space-y-4">
                <!-- Upload button -->
                <div x-show="enableUploader">
                    <input 
                        type="file" 
                        :accept="acceptedTypes"
                        :multiple="isMultiple"
                        @change="handleTemporaryUpload($event)"
                        class="hidden"
                        x-ref="fileInput"
                    />
                    <button 
                        type="button"
                        @click="$refs.fileInput.click()"
                        :disabled="isUploading"
                        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                        <svg x-show="!isUploading" class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <svg x-show="isUploading" class="animate-spin w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span x-text="isUploading ? 'Uploading...' : 'Upload Gambar'"></span>
                    </button>
                </div>

                <!-- Picker button -->
                <div x-show="enablePicker">
                    <button 
                        type="button"
                        @click="openPicker()"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Pilih dari Galeri
                    </button>
                </div>

                <!-- Placeholder text -->
                <p class="text-sm text-gray-500">
                    {{ $getPlaceholder() ?? 'Upload gambar baru atau pilih dari galeri' }}
                </p>
            </div>
        </div>

        <!-- Temporary files info -->
        <div x-show="temporaryFiles.length > 0" class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <span class="text-sm text-yellow-800">
                    <span x-text="temporaryFiles.length"></span> file temporary akan dipindahkan saat form disimpan
                </span>
            </div>
        </div>
    </div>

    <!-- Notification area -->
    <div x-show="notification.show" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform translate-y-2"
         class="fixed bottom-4 right-4 z-50">
        <div :class="notification.type === 'success' ? 'bg-green-500' : 'bg-red-500'" 
             class="text-white px-6 py-3 rounded-lg shadow-lg">
            <span x-text="notification.message"></span>
        </div>
    </div>

    <!-- Modal picker (akan dimuat dari RajaPicker original) -->
    <div x-show="showPicker" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-50 overflow-y-auto"
         style="display: none;">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closePicker()"></div>
            
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <!-- Modal content akan dimuat via AJAX -->
                <div x-html="pickerContent"></div>
            </div>
        </div>
    </div>
  </div>
</x-dynamic-component>

@push('scripts')
<script>
function rajaPickerExt(config) {
    return {
        // Konfigurasi
        statePath: config.statePath,
        isMultiple: config.isMultiple,
        collection: config.collection,
        maxFileSize: config.maxFileSize,
        acceptedTypes: config.acceptedTypes,
        enablePicker: config.enablePicker,
        enableUploader: config.enableUploader,
        previewSize: config.previewSize,
        byUser: config.byUser,
        byUserId: config.byUserId,
        convertWebp: config.convertWebp,
        useTemporaryUpload: config.useTemporaryUpload,

        // State
        value: config.currentValue || (config.isMultiple ? [] : null),
        isUploading: false,
        showPicker: false,
        pickerContent: '',
        temporaryFiles: [],
        
        // Notification
        notification: {
            show: false,
            message: '',
            type: 'success'
        },

        // Computed properties
        get hasValue() {
            if (this.isMultiple) {
                return Array.isArray(this.value) && this.value.length > 0;
            }
            return this.value !== null && this.value !== '';
        },

        // Initialize
        init() {
            console.log('RajaPickerExt initialized:', {
                statePath: this.statePath,
                isMultiple: this.isMultiple,
                collection: this.collection,
                useTemporaryUpload: this.useTemporaryUpload,
                currentValue: this.value
            });
        },

        // Handle temporary file upload
        async handleTemporaryUpload(event) {
            this.isUploading = true;
            const files = Array.from(event.target.files);
            
            if (files.length === 0) {
                this.isUploading = false;
                return;
            }

            // Validate files
            for (const file of files) {
                if (!this.validateFile(file)) {
                    this.isUploading = false;
                    return;
                }
            }

            try {
                const formData = new FormData();
                
                if (this.isMultiple) {
                    files.forEach((file, index) => {
                        formData.append(`files[${index}]`, file);
                    });
                } else {
                    formData.append('file', files[0]);
                }

                formData.append('collection', this.collection);
                formData.append('temporary', 'true'); // Flag untuk temporary upload
                formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

                const response = await fetch('/api/media/upload-temporary', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    this.handleTemporaryUploadSuccess(result);
                } else {
                    const error = await response.json();
                    this.showNotification(error.message || 'Gagal mengupload gambar', 'error');
                }
            } catch (error) {
                console.error('Upload error:', error);
                this.showNotification('Terjadi kesalahan saat mengupload', 'error');
            } finally {
                this.isUploading = false;
                // Reset file input
                event.target.value = '';
            }
        },

        // Handle successful temporary upload
        handleTemporaryUploadSuccess(result) {
            if (this.isMultiple) {
                if (Array.isArray(result.files)) {
                    result.files.forEach(file => {
                        this.temporaryFiles.push(file);
                        if (!Array.isArray(this.value)) {
                            this.value = [];
                        }
                        // Kirim temp_path ke backend, bukan temporary_url
                        this.value.push(file.temp_path);
                    });
                }
            } else {
                if (result.file) {
                    this.temporaryFiles = [result.file];
                    // Kirim temp_path ke backend, bukan temporary_url
                    this.value = result.file.temp_path;
                }
            }

            this.syncWithLivewire(this.value);
            this.showNotification('File berhasil diupload ke temporary storage!', 'success');
        },

        // Validate file
        validateFile(file) {
            // Check file type
            const acceptedTypesArray = this.acceptedTypes.split(',');
            if (!acceptedTypesArray.includes(file.type)) {
                this.showNotification(`Tipe file ${file.type} tidak diizinkan`, 'error');
                return false;
            }

            // Check file size (convert MB to bytes)
            const maxSizeBytes = this.maxFileSize * 1024 * 1024;
            if (file.size > maxSizeBytes) {
                this.showNotification(`Ukuran file terlalu besar. Maksimal ${this.maxFileSize}MB`, 'error');
                return false;
            }

            return true;
        },

        // Get preview URL
        getPreviewUrl(url) {
            if (!url) return '';

            // Jika URL sudah lengkap, gunakan langsung
            if (url.startsWith('http')) {
                return url;
            }

            // Jika file temporary (path dimulai dengan livewire-tmp/), gunakan endpoint khusus
            if (url.startsWith('livewire-tmp/')) {
                return '/api/media/temporary-preview?path=' + encodeURIComponent(url);
            }

            // Jika file temporary dengan prefix /storage/, gunakan endpoint khusus
            if (url.startsWith('/storage/livewire-tmp/')) {
                return '/api/media/temporary-preview?path=' + encodeURIComponent(url.replace('/storage/', ''));
            }

            // Jika sudah dimulai dengan /storage/, gunakan langsung
            if (url.startsWith('/storage/')) {
                return url;
            }

            // Tambahkan prefix /storage/ jika belum ada
            return '/storage/' + url;
        },

        // Get file name from URL
        getFileName(url) {
            if (!url) return '';
            return url.split('/').pop() || '';
        },

        // Remove value
        removeValue() {
            this.value = this.isMultiple ? [] : null;
            this.temporaryFiles = [];
            this.syncWithLivewire(this.value);
        },

        // Remove value at index (for multiple)
        removeValueAt(index) {
            if (Array.isArray(this.value)) {
                this.value.splice(index, 1);
                this.temporaryFiles.splice(index, 1);
                this.syncWithLivewire(this.value);
            }
        },

        // Open picker modal
        async openPicker() {
            try {
                const response = await fetch(`/api/media/picker?collection=${this.collection}&multiple=${this.isMultiple}&byUser=${this.byUser}&byUserId=${this.byUserId}`);
                if (response.ok) {
                    this.pickerContent = await response.text();
                    this.showPicker = true;
                }
            } catch (error) {
                console.error('Error loading picker:', error);
                this.showNotification('Gagal memuat galeri', 'error');
            }
        },

        // Close picker modal
        closePicker() {
            this.showPicker = false;
            this.pickerContent = '';
        },

        // Sync with Livewire
        syncWithLivewire(value) {
            this.$wire.set(this.statePath, value);
        },

        // Show notification
        showNotification(message, type = 'success') {
            this.notification = {
                show: true,
                message: message,
                type: type
            };

            setTimeout(() => {
                this.notification.show = false;
            }, 3000);
        }
    };
}
</script>
@endpush
